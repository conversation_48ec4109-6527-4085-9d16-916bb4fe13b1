import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';

class TranslationHelper {
  TranslationHelper._();

  static String getDeviceLanguage(BuildContext context) {
    var deviceLanguage = context.deviceLocale.countryCode!.toLowerCase();
    print(deviceLanguage);
    switch (deviceLanguage) {
      case 'tr':
        return 'tr';
      case 'en':
        return 'en';
      default:
        return 'en';
    }
  }
}
